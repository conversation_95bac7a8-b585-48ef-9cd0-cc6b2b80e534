# 权限系统快速入门指南

## 🚀 5分钟快速上手

### 第一步：理解核心概念

```
用户(User) → 拥有 → 角色(Role) → 包含 → 权限(Permission)
```

- **用户**: 系统的使用者（如：张三、李四）
- **角色**: 用户的身份（如：管理员、编辑员、普通用户）
- **权限**: 具体的操作权限（如：查看用户、创建文章、删除数据）

### 第二步：运行示例代码

```bash
# 运行权限系统示例
python docs/权限系统示例代码.py
```

你会看到不同用户的权限测试结果：
- 管理员：拥有所有权限 ✅
- 普通用户：只能查看和更新个人资料 ❌
- 编辑员：可以管理文章但不能管理用户 ⚠️

### 第三步：理解装饰器

```python
@require_permission("user:read")  # 这是装饰器
def get_user_list():              # 这是被装饰的函数
    return "用户列表"
```

装饰器的作用：在函数执行前检查权限，如果没有权限就阻止执行。

## 🎯 核心代码解析

### 1. 权限检查函数

<augment_code_snippet path="docs/权限系统示例代码.py" mode="EXCERPT">
````python
def check_permission(user: User, required_permission: str) -> bool:
    """检查用户是否拥有指定权限"""
    user_permissions = get_user_permissions(user)
    
    # 检查是否有超级权限
    if "*:*:*" in user_permissions:
        return True
    
    # 精确匹配
    if required_permission in user_permissions:
        return True
    
    # 通配符匹配
    for perm in user_permissions:
        if perm.endswith("*"):
            prefix = perm[:-1]
            if required_permission.startswith(prefix):
                return True
    
    return False
````
</augment_code_snippet>

### 2. 权限装饰器

<augment_code_snippet path="docs/权限系统示例代码.py" mode="EXCERPT">
````python
def require_permission(permission_code: str):
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            if not current_user:
                raise PermissionError("用户未登录")
            
            if not check_permission(current_user, permission_code):
                raise PermissionError(f"权限不足，需要权限: {permission_code}")
            
            return func(*args, **kwargs)
        return wrapper
    return decorator
````
</augment_code_snippet>

## 🛠️ 实际项目中的应用

### 在FastAPI中使用权限装饰器

<augment_code_snippet path="backend/common/security/java_permission.py" mode="EXCERPT">
````python
@router.get("/users")
@require_java_permission("system:user:list")
async def get_users():
    # 只有拥有 system:user:list 权限的用户才能访问
    return {"users": []}
````
</augment_code_snippet>

### JWT认证流程

<augment_code_snippet path="backend/common/security/jwt.py" mode="EXCERPT">
````python
async def jwt_authentication(token: str) -> GetUserInfoWithRelationDetail:
    # 首先尝试 FBA 系统的 JWT 认证
    try:
        token_payload = jwt_decode(token)
        # ... 验证逻辑
        return user
    except errors.TokenError:
        # FBA token 验证失败，尝试 Java token 认证
        if java_adapter.is_enabled():
            return await java_adapter.authenticate_java_token(token)
        raise errors.TokenError(msg='Token 无效或已过期')
````
</augment_code_snippet>

### 中间件权限检查

<augment_code_snippet path="backend/middleware/java_permission_middleware.py" mode="EXCERPT">
````python
async def dispatch(self, request: Request, call_next: Callable) -> Response:
    # 1. 检查是否需要权限验证
    if not self._need_permission_check(request):
        return await call_next(request)
    
    # 2. 获取用户ID
    user_id = self._get_user_id_from_request(request)
    
    # 3. 检查用户权限
    has_permission = await java_permission_service.check_user_permission(
        user_id, required_permission
    )
    
    if not has_permission:
        raise HTTPException(status_code=403, detail="权限不足")
    
    return await call_next(request)
````
</augment_code_snippet>

## 📚 学习路径

### 初级（1-2周）
1. ✅ 理解用户、角色、权限的概念
2. ✅ 学会使用装饰器进行权限控制
3. ✅ 掌握基本的权限检查逻辑
4. ✅ 运行和修改示例代码

### 中级（2-4周）
1. 🔄 学习JWT认证机制
2. 🔄 理解中间件的作用和实现
3. 🔄 掌握异步编程基础
4. 🔄 学习数据库权限设计

### 高级（1-2个月）
1. ⏳ 权限系统性能优化
2. ⏳ 分布式权限管理
3. ⏳ 安全最佳实践
4. ⏳ 权限系统监控和审计

## 🎯 实践练习

### 练习1：添加新权限
在示例代码中添加一个新的权限类型：

```python
# 1. 在PERMISSIONS中添加新权限
Permission("comment:create", "评论创建", "创建评论")

# 2. 给某个角色添加这个权限
ROLES["user"].permissions.append("comment:create")

# 3. 创建需要这个权限的函数
@require_permission("comment:create")
def create_comment(content: str):
    return f"评论创建成功: {content}"
```

### 练习2：创建新角色
创建一个"版主"角色：

```python
# 1. 定义版主角色
ROLES["moderator"] = Role(4, "版主", [
    "user:read", 
    "comment:*",  # 所有评论权限
    "article:read"
])

# 2. 创建版主用户
USERS[4] = User(4, "版主小王", ["moderator"])

# 3. 测试版主权限
login(4)
test_operations()
```

### 练习3：实现权限继承
让子角色继承父角色的权限：

```python
def get_inherited_permissions(role_name: str) -> List[str]:
    """获取角色的继承权限"""
    # 定义角色继承关系
    inheritance = {
        "admin": [],  # 管理员不继承
        "editor": ["user"],  # 编辑员继承普通用户权限
        "moderator": ["user"],  # 版主继承普通用户权限
        "user": []  # 普通用户不继承
    }
    
    permissions = []
    
    # 添加继承的权限
    if role_name in inheritance:
        for parent_role in inheritance[role_name]:
            permissions.extend(ROLES[parent_role].permissions)
    
    # 添加自己的权限
    if role_name in ROLES:
        permissions.extend(ROLES[role_name].permissions)
    
    return list(set(permissions))  # 去重
```

## 🔧 常用工具函数

### 权限检查工具

```python
def has_any_permission(user: User, permissions: List[str]) -> bool:
    """检查用户是否拥有任意一个权限"""
    for perm in permissions:
        if check_permission(user, perm):
            return True
    return False

def has_all_permissions(user: User, permissions: List[str]) -> bool:
    """检查用户是否拥有所有权限"""
    for perm in permissions:
        if not check_permission(user, perm):
            return False
    return True

def get_missing_permissions(user: User, required_permissions: List[str]) -> List[str]:
    """获取用户缺少的权限"""
    missing = []
    for perm in required_permissions:
        if not check_permission(user, perm):
            missing.append(perm)
    return missing
```

### 权限管理工具

```python
def grant_permission_to_user(user_id: int, permission: str):
    """给用户授予权限（通过角色）"""
    # 实际项目中这会更新数据库
    pass

def revoke_permission_from_user(user_id: int, permission: str):
    """撤销用户权限"""
    # 实际项目中这会更新数据库
    pass

def list_user_permissions(user_id: int) -> List[str]:
    """列出用户的所有权限"""
    if user_id in USERS:
        return get_user_permissions(USERS[user_id])
    return []
```

## 🚨 常见错误和解决方案

### 错误1：装饰器顺序错误
```python
# ❌ 错误的顺序
@require_permission("user:read")
@app.get("/users")
async def get_users():
    pass

# ✅ 正确的顺序
@app.get("/users")
@require_permission("user:read")
async def get_users():
    pass
```

### 错误2：权限代码拼写错误
```python
# ❌ 拼写错误
@require_permission("user:raed")  # raed应该是read

# ✅ 正确拼写
@require_permission("user:read")
```

### 错误3：忘记处理异常
```python
# ❌ 没有处理权限异常
def some_operation():
    get_user_list()  # 可能抛出PermissionError

# ✅ 正确处理异常
def some_operation():
    try:
        return get_user_list()
    except PermissionError as e:
        return {"error": str(e)}
```

## 🎉 下一步

1. **阅读完整教学文档**: `docs/Python初学者权限系统教学文档.md`
2. **查看实际项目代码**: 研究`backend/common/security/`目录下的文件
3. **运行项目**: 使用`python backend/start_stable.py`启动完整项目
4. **实践修改**: 尝试添加新的权限类型和角色
5. **深入学习**: 学习FastAPI、SQLAlchemy、Redis等相关技术

记住：**实践是最好的学习方式**！不要只是阅读代码，要动手修改和测试。

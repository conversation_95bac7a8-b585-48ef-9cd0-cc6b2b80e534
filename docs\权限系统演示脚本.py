#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
权限系统演示脚本 - 用于技术团队讲解
展示权限系统的核心功能和调用流程
"""

import asyncio
import json
import time
from typing import Dict, List, Optional
import base64
import re

# 模拟导入（实际演示时使用真实导入）
# from backend.common.security.java_adapter import java_adapter
# from backend.service.java_permission_service import java_permission_service

class DemoJavaAdapter:
    """演示用Java适配器"""
    
    def __init__(self):
        self.enabled = True
        
    async def authenticate_java_token(self, token: str) -> Dict:
        """演示Java token认证过程"""
        print("🔐 开始Java Token认证...")
        
        try:
            # 1. 演示JWT解析
            print("📝 步骤1: 解析JWT Token")
            parts = token.split('.')
            if len(parts) != 3:
                raise Exception('Token 格式错误')
            
            # 解析header
            header_data = parts[0] + '=' * (4 - len(parts[0]) % 4)
            header = json.loads(base64.urlsafe_b64decode(header_data))
            print(f"   Header: {header}")
            
            # 解析payload
            payload_data = parts[1] + '=' * (4 - len(parts[1]) % 4)
            payload = json.loads(base64.urlsafe_b64decode(payload_data))
            print(f"   Payload: {payload}")
            
            uuid = payload.get("login_user_key", "demo-uuid-123")
            print(f"   提取UUID: {uuid}")
            
            # 2. 演示Redis查询
            print("📝 步骤2: 从Redis获取用户数据")
            redis_key = f"login_tokens:{uuid}"
            print(f"   Redis Key: {redis_key}")
            
            # 模拟Java用户数据
            java_user_data = {
                "user": {
                    "userId": 1,
                    "userName": "admin",
                    "nickName": "管理员",
                    "email": "<EMAIL>",
                    "admin": True,
                    "roles": [
                        {"roleId": 1, "roleName": "管理员", "roleKey": "admin"}
                    ]
                },
                "permissions": ["*:*:*"],
                "expireTime": int(time.time() * 1000) + 3600000  # 1小时后过期
            }
            
            # 3. 演示JSON格式修复
            print("📝 步骤3: 修复Java JSON格式")
            java_json = json.dumps(java_user_data).replace('["*:*:*"]', 'Set["*:*:*"]')
            print(f"   原始Java JSON: {java_json[:100]}...")
            
            fixed_json = self._parse_java_json(java_json)
            print(f"   修复后JSON: {json.dumps(fixed_json)[:100]}...")
            
            # 4. 演示数据转换
            print("📝 步骤4: 转换为FBA格式")
            fba_user = self._convert_java_user_to_fba(fixed_json)
            print(f"   FBA用户: {fba_user}")
            
            print("✅ Java Token认证成功!")
            return fba_user
            
        except Exception as e:
            print(f"❌ Java Token认证失败: {e}")
            raise
    
    def _parse_java_json(self, json_str: str) -> Dict:
        """演示Java JSON格式修复"""
        # 修复Java集合格式
        fixed_json = re.sub(r'Set\[([^\]]+)\]', r'[\1]', json_str)
        fixed_json = re.sub(r'List\[([^\]]+)\]', r'[\1]', fixed_json)
        return json.loads(fixed_json)
    
    def _convert_java_user_to_fba(self, java_user: Dict) -> Dict:
        """演示Java用户数据转换"""
        user_info = java_user.get('user', {})
        return {
            'id': user_info.get('userId', 1),
            'username': user_info.get('userName', 'admin'),
            'nickname': user_info.get('nickName', '管理员'),
            'email': user_info.get('email'),
            'is_superuser': user_info.get('admin', False),
            'permissions': java_user.get('permissions', [])
        }


class DemoPermissionService:
    """演示用权限服务"""
    
    def __init__(self):
        # 模拟用户权限数据
        self.user_permissions = {
            1: {  # 管理员
                "user_id": 1,
                "username": "admin",
                "is_admin": True,
                "permission_codes": ["*:*:*"],
                "roles": [{"role_id": 1, "role_name": "管理员"}]
            },
            2: {  # 普通用户
                "user_id": 2,
                "username": "user",
                "is_admin": False,
                "permission_codes": ["knowledge:base:list", "user:profile:update"],
                "roles": [{"role_id": 2, "role_name": "普通用户"}]
            },
            3: {  # 编辑员
                "user_id": 3,
                "username": "editor",
                "is_admin": False,
                "permission_codes": ["knowledge:*", "article:*"],
                "roles": [{"role_id": 3, "role_name": "编辑员"}]
            }
        }
    
    async def get_user_permissions(self, user_id: int) -> Dict:
        """演示获取用户权限"""
        print(f"🔍 查询用户权限: user_id={user_id}")
        
        if user_id not in self.user_permissions:
            print(f"❌ 用户不存在: {user_id}")
            return {"error": "用户不存在"}
        
        permissions = self.user_permissions[user_id]
        print(f"✅ 权限查询成功: {permissions}")
        return permissions
    
    async def check_user_permission(self, user_id: int, permission_code: str) -> bool:
        """演示权限检查过程"""
        print(f"\n🛡️  权限检查: user_id={user_id}, permission={permission_code}")
        
        # 1. 获取用户权限
        permission_info = await self.get_user_permissions(user_id)
        if "error" in permission_info:
            print("❌ 权限检查失败: 用户不存在")
            return False
        
        permission_codes = permission_info.get("permission_codes", [])
        print(f"   用户权限列表: {permission_codes}")
        
        # 2. 权限匹配算法演示
        print("   开始权限匹配...")
        
        # 超级权限检查
        if "*:*:*" in permission_codes:
            print("   ✅ 匹配成功: 超级管理员权限")
            return True
        
        # 精确匹配
        if permission_code in permission_codes:
            print("   ✅ 匹配成功: 精确匹配")
            return True
        
        # 通配符匹配
        for code in permission_codes:
            if code.endswith("*"):
                prefix = code[:-1]
                if permission_code.startswith(prefix):
                    print(f"   ✅ 匹配成功: 通配符匹配 ({code})")
                    return True
        
        print("   ❌ 匹配失败: 权限不足")
        return False


class DemoPermissionDecorator:
    """演示权限装饰器"""
    
    def __init__(self, permission_service):
        self.permission_service = permission_service
        self.current_user_id = None
    
    def require_permission(self, permission_code: str):
        """演示权限装饰器"""
        def decorator(func):
            async def wrapper(*args, **kwargs):
                print(f"\n🎯 权限装饰器检查: {permission_code}")
                
                # 模拟获取用户ID
                if not self.current_user_id:
                    print("❌ 用户未登录")
                    raise Exception("用户未登录")
                
                # 权限检查
                has_permission = await self.permission_service.check_user_permission(
                    self.current_user_id, permission_code
                )
                
                if not has_permission:
                    print(f"❌ 权限不足，需要权限: {permission_code}")
                    raise Exception(f"权限不足，需要权限: {permission_code}")
                
                print("✅ 权限检查通过，执行业务逻辑")
                return await func(*args, **kwargs)
            
            return wrapper
        return decorator
    
    def set_current_user(self, user_id: int):
        """设置当前用户"""
        self.current_user_id = user_id
        print(f"👤 设置当前用户: user_id={user_id}")


async def demo_jwt_authentication():
    """演示JWT认证过程"""
    print("=" * 60)
    print("🔐 JWT认证演示")
    print("=" * 60)
    
    # 创建演示用的JWT token
    header = {"typ": "JWT", "alg": "HS512"}
    payload = {"login_user_key": "demo-uuid-123", "sub": "1", "iat": int(time.time())}
    
    # 简单编码（实际应该有签名）
    header_b64 = base64.urlsafe_b64encode(json.dumps(header).encode()).decode().rstrip('=')
    payload_b64 = base64.urlsafe_b64encode(json.dumps(payload).encode()).decode().rstrip('=')
    signature = "demo-signature"
    
    demo_token = f"{header_b64}.{payload_b64}.{signature}"
    print(f"📝 演示Token: {demo_token[:50]}...")
    
    # 演示认证过程
    adapter = DemoJavaAdapter()
    try:
        user = await adapter.authenticate_java_token(demo_token)
        print(f"🎉 认证成功: {user['username']}")
    except Exception as e:
        print(f"💥 认证失败: {e}")


async def demo_permission_check():
    """演示权限检查过程"""
    print("\n" + "=" * 60)
    print("🛡️  权限检查演示")
    print("=" * 60)
    
    service = DemoPermissionService()
    
    # 测试不同用户的权限
    test_cases = [
        (1, "knowledge:base:list", "管理员访问知识库列表"),
        (2, "knowledge:base:list", "普通用户访问知识库列表"),
        (2, "knowledge:base:delete", "普通用户删除知识库"),
        (3, "knowledge:base:create", "编辑员创建知识库"),
        (3, "system:user:list", "编辑员查看用户列表"),
    ]
    
    for user_id, permission, description in test_cases:
        print(f"\n📋 测试场景: {description}")
        result = await service.check_user_permission(user_id, permission)
        status = "✅ 允许" if result else "❌ 拒绝"
        print(f"   结果: {status}")


async def demo_decorator_usage():
    """演示装饰器使用"""
    print("\n" + "=" * 60)
    print("🎯 装饰器使用演示")
    print("=" * 60)
    
    service = DemoPermissionService()
    decorator = DemoPermissionDecorator(service)
    
    # 定义带权限的业务函数
    @decorator.require_permission("knowledge:base:list")
    async def get_knowledge_bases():
        return {"data": ["知识库1", "知识库2", "知识库3"]}
    
    @decorator.require_permission("knowledge:base:create")
    async def create_knowledge_base(name: str):
        return {"message": f"知识库 '{name}' 创建成功"}
    
    # 测试不同用户访问
    test_users = [1, 2, 3]  # 管理员、普通用户、编辑员
    
    for user_id in test_users:
        print(f"\n👤 测试用户: {user_id}")
        decorator.set_current_user(user_id)
        
        # 测试查看知识库列表
        try:
            result = await get_knowledge_bases()
            print(f"   📋 获取知识库列表: {result}")
        except Exception as e:
            print(f"   📋 获取知识库列表: {e}")
        
        # 测试创建知识库
        try:
            result = await create_knowledge_base("新知识库")
            print(f"   ➕ 创建知识库: {result}")
        except Exception as e:
            print(f"   ➕ 创建知识库: {e}")


async def demo_middleware_logic():
    """演示中间件逻辑"""
    print("\n" + "=" * 60)
    print("⚡ 中间件逻辑演示")
    print("=" * 60)
    
    # 模拟HTTP请求
    class MockRequest:
        def __init__(self, path: str, method: str = "GET"):
            self.url = type('obj', (object,), {'path': path})()
            self.method = method
            self.state = type('obj', (object,), {'user_id': None})()
    
    # 权限映射表
    permission_mapping = {
        "/api/iot/v1/knowledge-base/list": "knowledge:base:list",
        "/api/iot/v1/knowledge-base/create": "knowledge:base:create",
        "/api/iot/v1/knowledge-base/update": "knowledge:base:update",
        "/api/iot/v1/knowledge-base/delete": "knowledge:base:delete",
    }
    
    # 排除路径
    exclude_paths = ["/docs", "/redoc", "/api/v1/auth/login"]
    
    service = DemoPermissionService()
    
    # 测试请求
    test_requests = [
        ("/docs", "GET", None, "访问API文档"),
        ("/api/v1/auth/login", "POST", None, "用户登录"),
        ("/api/iot/v1/knowledge-base/list", "GET", 1, "管理员查看知识库"),
        ("/api/iot/v1/knowledge-base/list", "GET", 2, "普通用户查看知识库"),
        ("/api/iot/v1/knowledge-base/delete", "DELETE", 2, "普通用户删除知识库"),
    ]
    
    for path, method, user_id, description in test_requests:
        print(f"\n🌐 请求: {method} {path} - {description}")
        
        request = MockRequest(path, method)
        request.state.user_id = user_id
        
        # 1. 检查是否需要权限验证
        need_check = not any(path.startswith(exclude) for exclude in exclude_paths)
        print(f"   需要权限检查: {need_check}")
        
        if not need_check:
            print("   ✅ 跳过权限检查，直接放行")
            continue
        
        # 2. 检查用户登录
        if not user_id:
            print("   ❌ 用户未登录，拒绝访问")
            continue
        
        # 3. 获取所需权限
        required_permission = permission_mapping.get(path)
        print(f"   所需权限: {required_permission}")
        
        if not required_permission:
            print("   ✅ 无权限要求，直接放行")
            continue
        
        # 4. 权限检查
        has_permission = await service.check_user_permission(user_id, required_permission)
        if has_permission:
            print("   ✅ 权限检查通过，放行请求")
        else:
            print("   ❌ 权限不足，拒绝访问")


async def main():
    """主演示函数"""
    print("🎓 权限系统核心功能演示")
    print("适用于技术团队讲解和学习")
    print()
    
    # 1. JWT认证演示
    await demo_jwt_authentication()
    
    # 2. 权限检查演示
    await demo_permission_check()
    
    # 3. 装饰器使用演示
    await demo_decorator_usage()
    
    # 4. 中间件逻辑演示
    await demo_middleware_logic()
    
    print("\n" + "=" * 60)
    print("🎉 演示完成!")
    print("💡 这些演示展示了权限系统的核心工作原理")
    print("📚 可以结合实际代码进行深入学习")
    print("=" * 60)


if __name__ == "__main__":
    asyncio.run(main())

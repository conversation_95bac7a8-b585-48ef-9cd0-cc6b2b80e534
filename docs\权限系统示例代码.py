#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
权限系统示例代码 - 适合Python初学者学习
这个文件包含了权限系统的核心概念和简化实现
"""

from functools import wraps
from typing import Dict, List, Optional
import asyncio
import time
import json

# ================================
# 1. 基础数据结构定义
# ================================

class User:
    """用户类"""
    def __init__(self, user_id: int, username: str, roles: List[str] = None):
        self.user_id = user_id
        self.username = username
        self.roles = roles or []
        self.is_admin = user_id == 1  # 用户ID为1的是管理员

class Permission:
    """权限类"""
    def __init__(self, code: str, name: str, description: str = ""):
        self.code = code  # 权限代码，如: "user:read"
        self.name = name  # 权限名称，如: "用户查看"
        self.description = description

class Role:
    """角色类"""
    def __init__(self, role_id: int, name: str, permissions: List[str] = None):
        self.role_id = role_id
        self.name = name
        self.permissions = permissions or []

# ================================
# 2. 模拟数据库数据
# ================================

# 模拟用户数据
USERS = {
    1: User(1, "admin", ["admin"]),
    2: User(2, "张三", ["user"]),
    3: User(3, "李四", ["editor"]),
}

# 模拟角色数据
ROLES = {
    "admin": Role(1, "管理员", ["*:*:*"]),  # 管理员拥有所有权限
    "user": Role(2, "普通用户", ["user:read", "profile:update"]),
    "editor": Role(3, "编辑员", ["user:read", "article:*"]),
}

# 模拟权限数据
PERMISSIONS = [
    Permission("user:read", "用户查看", "查看用户信息"),
    Permission("user:create", "用户创建", "创建新用户"),
    Permission("user:update", "用户更新", "更新用户信息"),
    Permission("user:delete", "用户删除", "删除用户"),
    Permission("article:read", "文章查看", "查看文章"),
    Permission("article:create", "文章创建", "创建文章"),
    Permission("article:update", "文章更新", "更新文章"),
    Permission("article:delete", "文章删除", "删除文章"),
    Permission("profile:update", "个人资料更新", "更新个人资料"),
]

# 当前登录用户（模拟）
current_user: Optional[User] = None

# ================================
# 3. 权限检查核心函数
# ================================

def get_user_permissions(user: User) -> List[str]:
    """获取用户的所有权限"""
    if user.is_admin:
        return ["*:*:*"]  # 管理员拥有所有权限
    
    permissions = []
    for role_name in user.roles:
        if role_name in ROLES:
            permissions.extend(ROLES[role_name].permissions)
    
    return list(set(permissions))  # 去重

def check_permission(user: User, required_permission: str) -> bool:
    """检查用户是否拥有指定权限"""
    user_permissions = get_user_permissions(user)
    
    # 检查是否有超级权限
    if "*:*:*" in user_permissions:
        return True
    
    # 精确匹配
    if required_permission in user_permissions:
        return True
    
    # 通配符匹配
    for perm in user_permissions:
        if perm.endswith("*"):
            prefix = perm[:-1]
            if required_permission.startswith(prefix):
                return True
    
    return False

# ================================
# 4. 权限装饰器实现
# ================================

def require_permission(permission_code: str):
    """
    权限装饰器 - 要求特定权限
    
    使用方法:
    @require_permission("user:read")
    def get_user_info():
        return "用户信息"
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 检查是否有当前用户
            if not current_user:
                raise PermissionError("用户未登录")
            
            # 检查权限
            if not check_permission(current_user, permission_code):
                raise PermissionError(f"权限不足，需要权限: {permission_code}")
            
            print(f"✅ 权限检查通过: {current_user.username} -> {permission_code}")
            return func(*args, **kwargs)
        
        return wrapper
    return decorator

def require_login(func):
    """
    登录装饰器 - 要求用户已登录
    """
    @wraps(func)
    def wrapper(*args, **kwargs):
        if not current_user:
            raise PermissionError("请先登录")
        
        print(f"✅ 登录检查通过: {current_user.username}")
        return func(*args, **kwargs)
    
    return wrapper

# ================================
# 5. 业务函数示例
# ================================

@require_permission("user:read")
def get_user_list():
    """获取用户列表 - 需要用户查看权限"""
    return [{"id": uid, "username": user.username} for uid, user in USERS.items()]

@require_permission("user:create")
def create_user(username: str):
    """创建用户 - 需要用户创建权限"""
    new_id = max(USERS.keys()) + 1
    USERS[new_id] = User(new_id, username, ["user"])
    return f"用户 {username} 创建成功"

@require_permission("user:delete")
def delete_user(user_id: int):
    """删除用户 - 需要用户删除权限"""
    if user_id in USERS:
        username = USERS[user_id].username
        del USERS[user_id]
        return f"用户 {username} 删除成功"
    return "用户不存在"

@require_login
def get_profile():
    """获取个人资料 - 只需要登录"""
    return {
        "user_id": current_user.user_id,
        "username": current_user.username,
        "roles": current_user.roles
    }

@require_permission("article:create")
def create_article(title: str, content: str):
    """创建文章 - 需要文章创建权限"""
    return f"文章《{title}》创建成功"

# ================================
# 6. 用户登录/登出功能
# ================================

def login(user_id: int) -> bool:
    """用户登录"""
    global current_user
    
    if user_id in USERS:
        current_user = USERS[user_id]
        print(f"🔐 用户登录成功: {current_user.username}")
        return True
    else:
        print("❌ 用户不存在")
        return False

def logout():
    """用户登出"""
    global current_user
    
    if current_user:
        print(f"👋 用户登出: {current_user.username}")
        current_user = None
    else:
        print("❌ 没有用户登录")

def get_current_user() -> Optional[User]:
    """获取当前登录用户"""
    return current_user

# ================================
# 7. 权限管理功能
# ================================

def show_user_permissions(user_id: int):
    """显示用户权限"""
    if user_id not in USERS:
        print("❌ 用户不存在")
        return
    
    user = USERS[user_id]
    permissions = get_user_permissions(user)
    
    print(f"\n👤 用户: {user.username}")
    print(f"🏷️  角色: {', '.join(user.roles)}")
    print(f"🔑 权限:")
    for perm in permissions:
        print(f"   - {perm}")

def show_all_permissions():
    """显示所有可用权限"""
    print("\n📋 系统所有权限:")
    for perm in PERMISSIONS:
        print(f"   {perm.code} - {perm.name}: {perm.description}")

# ================================
# 8. 测试和演示函数
# ================================

def demo_permission_system():
    """演示权限系统的使用"""
    print("=" * 50)
    print("🎯 权限系统演示")
    print("=" * 50)
    
    # 显示所有权限
    show_all_permissions()
    
    # 测试不同用户的权限
    for user_id in [1, 2, 3]:
        print(f"\n{'='*30}")
        print(f"测试用户 ID: {user_id}")
        print(f"{'='*30}")
        
        # 登录用户
        if login(user_id):
            # 显示用户权限
            show_user_permissions(user_id)
            
            # 测试各种操作
            test_operations()
            
            # 登出用户
            logout()
        
        print()

def test_operations():
    """测试各种操作"""
    operations = [
        ("查看个人资料", get_profile),
        ("获取用户列表", get_user_list),
        ("创建用户", lambda: create_user("新用户")),
        ("删除用户", lambda: delete_user(2)),
        ("创建文章", lambda: create_article("测试文章", "文章内容")),
    ]
    
    for op_name, op_func in operations:
        try:
            result = op_func()
            print(f"✅ {op_name}: {result}")
        except PermissionError as e:
            print(f"❌ {op_name}: {e}")
        except Exception as e:
            print(f"⚠️  {op_name}: 执行错误 - {e}")

# ================================
# 9. 高级功能：权限缓存
# ================================

class PermissionCache:
    """权限缓存类 - 提高性能"""
    
    def __init__(self, cache_timeout: int = 300):  # 5分钟缓存
        self.cache: Dict[int, Dict] = {}
        self.cache_timeout = cache_timeout
    
    def get_user_permissions(self, user_id: int) -> Optional[List[str]]:
        """从缓存获取用户权限"""
        if user_id in self.cache:
            cache_data = self.cache[user_id]
            if time.time() - cache_data['timestamp'] < self.cache_timeout:
                print(f"🚀 从缓存获取权限: user_id={user_id}")
                return cache_data['permissions']
        
        return None
    
    def set_user_permissions(self, user_id: int, permissions: List[str]):
        """设置用户权限到缓存"""
        self.cache[user_id] = {
            'permissions': permissions,
            'timestamp': time.time()
        }
        print(f"💾 权限已缓存: user_id={user_id}")
    
    def clear_user_cache(self, user_id: int):
        """清除用户权限缓存"""
        if user_id in self.cache:
            del self.cache[user_id]
            print(f"🗑️  权限缓存已清除: user_id={user_id}")

# 创建全局权限缓存实例
permission_cache = PermissionCache()

# ================================
# 10. 主程序入口
# ================================

if __name__ == "__main__":
    print("🎓 Python权限系统学习示例")
    print("这个示例展示了权限系统的核心概念和实现")
    print()
    
    # 运行演示
    demo_permission_system()
    
    print("\n" + "="*50)
    print("🎉 演示完成！")
    print("💡 你可以修改代码，尝试添加新的权限、角色或用户")
    print("📚 建议阅读代码注释，理解每个部分的作用")
    print("="*50)
